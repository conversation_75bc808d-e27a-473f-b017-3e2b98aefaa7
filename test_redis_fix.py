#!/usr/bin/env python3
"""
Test script to verify the Redis duplicate storage fix
"""
import redis
import json
import os
import ssl
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Redis configuration - Use AWS Redis settings
REDIS_HOST = os.getenv("REDIS_HOST", "master.redis-cache.icgeht.use1.cache.amazonaws.com")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_SSL = os.getenv("REDIS_SSL", "true").lower() == "true"

def test_redis_connection():
    """Test Redis connection and check for duplicates"""
    print("🔄 Testing Redis connection and checking for duplicates...")
    
    try:
        # Connect to Redis
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            db=REDIS_DB,
            ssl=REDIS_SSL,
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10,
            retry_on_timeout=True,
            ssl_min_version=ssl.TLSVersion.TLSv1_2 if REDIS_SSL else None
        )
        
        # Test connection
        response = redis_client.ping()
        print(f"✅ Redis connection successful: PING = {response}")
        
        # Check the specific session mentioned in the issue
        session_key = "session:688741d39de91bd34b104547:688741d39de91bd34b104548"
        messages = redis_client.lrange(session_key, 0, -1)
        
        if messages:
            print(f"\n📋 Found {len(messages)} messages in session:")
            print(f"Session key: {session_key}")
            print("-" * 60)
            
            parsed_messages = []
            for i, msg in enumerate(messages):
                try:
                    parsed_msg = json.loads(msg)
                    parsed_messages.append(parsed_msg)
                    
                    role = parsed_msg.get('role', 'unknown')
                    content = parsed_msg.get('content', '')[:50] + "..." if len(parsed_msg.get('content', '')) > 50 else parsed_msg.get('content', '')
                    timestamp = parsed_msg.get('timestamp', 'no timestamp')
                    msg_id = parsed_msg.get('id', 'no id')
                    
                    print(f"{i+1}. Role: {role}")
                    print(f"   ID: {msg_id}")
                    print(f"   Content: {content}")
                    print(f"   Timestamp: {timestamp}")
                    print(f"   Has feedback: {'feedback' in parsed_msg}")
                    print()
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Error parsing message {i+1}: {e}")
            
            # Check for duplicates
            print("🔍 Checking for duplicate queries...")
            user_queries = [msg for msg in parsed_messages if msg.get('role') == 'user']
            
            if len(user_queries) > 1:
                # Group by content
                query_groups = {}
                for query in user_queries:
                    content = query.get('content', '')
                    if content not in query_groups:
                        query_groups[content] = []
                    query_groups[content].append(query)
                
                duplicates_found = False
                for content, queries in query_groups.items():
                    if len(queries) > 1:
                        duplicates_found = True
                        print(f"⚠️ DUPLICATE FOUND: '{content}' appears {len(queries)} times")
                        for i, query in enumerate(queries):
                            print(f"   Instance {i+1}: ID={query.get('id')}, Timestamp={query.get('timestamp')}")
                
                if not duplicates_found:
                    print("✅ No duplicate queries found!")
            else:
                print("✅ Only one user query found, no duplicates possible.")
                
        else:
            print(f"⚠️ No messages found in session: {session_key}")
            
            # List all session keys to see what's available
            print("\n🔍 Searching for other session keys...")
            all_keys = redis_client.keys("session:*")
            if all_keys:
                print(f"Found {len(all_keys)} session keys:")
                for key in all_keys[:10]:  # Show first 10
                    msg_count = redis_client.llen(key)
                    print(f"  - {key}: {msg_count} messages")
                if len(all_keys) > 10:
                    print(f"  ... and {len(all_keys) - 10} more")
            else:
                print("No session keys found.")
        
        return True
        
    except redis.AuthenticationError as e:
        print(f"❌ Redis authentication failed: {e}")
        return False
    except redis.ConnectionError as e:
        print(f"❌ Redis connection failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def simulate_query_storage():
    """Simulate storing a query to test the fix"""
    print("\n🧪 Testing query storage simulation...")
    
    try:
        # Connect to Redis
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            db=REDIS_DB,
            ssl=REDIS_SSL,
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10,
            retry_on_timeout=True,
            ssl_min_version=ssl.TLSVersion.TLSv1_2 if REDIS_SSL else None
        )
        
        # Test session
        test_user_id = "test_user_123"
        test_chat_id = "507f1f77bcf86cd799439011"  # Valid ObjectId format
        test_query = "Test query for duplicate prevention"
        test_response = "Test response"
        
        session_key = f"session:{test_user_id}:{test_chat_id}"
        
        # Clear any existing test data
        redis_client.delete(session_key)
        
        # Simulate the fixed storage logic
        current_time = datetime.now().isoformat()
        
        user_message = {
            "id": "test_user_msg_id",
            "role": "user",
            "content": test_query,
            "timestamp": current_time,
            "metadata": {
                "chat_id": test_chat_id,
                "mode": "test",
                "host": "test",
                "model": "test"
            }
        }
        
        assistant_message = {
            "id": "test_assistant_msg_id",
            "role": "assistant",
            "content": test_response,
            "timestamp": current_time,
            "metadata": {
                "chat_id": test_chat_id,
                "sources": ["test"],
                "source_urls": [],
                "mode": "test",
                "host": "test",
                "model": "test"
            }
        }
        
        # First storage (simulating memory_manager.store_interaction)
        print("📝 First storage (memory_manager.store_interaction)...")
        redis_client.rpush(session_key, json.dumps(user_message))
        redis_client.rpush(session_key, json.dumps(assistant_message))
        redis_client.expire(session_key, 900)  # 15 minutes TTL
        
        # Check current state
        messages_after_first = redis_client.lrange(session_key, 0, -1)
        print(f"   Messages after first storage: {len(messages_after_first)}")
        
        # Second storage attempt (simulating append_to_chat_history with duplicate check)
        print("📝 Second storage attempt (append_to_chat_history with duplicate check)...")
        
        # Check if this query already exists
        existing_messages = redis_client.lrange(session_key, 0, -1)
        existing_messages = [json.loads(msg) for msg in existing_messages] if existing_messages else []
        
        query_exists = any(
            msg.get('role') == 'user' and 
            msg.get('content') == test_query and
            msg.get('metadata', {}).get('chat_id') == test_chat_id
            for msg in existing_messages
        )
        
        if query_exists:
            print("   ✅ Duplicate detected! Skipping storage.")
        else:
            print("   📝 No duplicate found, storing messages...")
            redis_client.rpush(session_key, json.dumps(user_message))
            redis_client.rpush(session_key, json.dumps(assistant_message))
        
        # Check final state
        messages_after_second = redis_client.lrange(session_key, 0, -1)
        print(f"   Messages after second storage attempt: {len(messages_after_second)}")
        
        if len(messages_after_first) == len(messages_after_second):
            print("✅ SUCCESS: Duplicate prevention working correctly!")
        else:
            print("❌ FAILURE: Duplicates were stored!")
        
        # Clean up
        redis_client.delete(session_key)
        print("🧹 Test data cleaned up.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in simulation: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Redis Duplicate Storage Fix Test")
    print("=" * 50)
    
    # Test 1: Check existing data
    success1 = test_redis_connection()
    
    # Test 2: Simulate the fix
    success2 = simulate_query_storage()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 All tests completed successfully!")
        print("✅ The Redis duplicate storage fix appears to be working.")
    else:
        print("❌ Some tests failed. Please check the output above.")
    
    print("\n💡 To test with your actual application:")
    print("1. Start your FastAPI server: python main12.py")
    print("2. Send a query via the /query endpoint")
    print("3. Check Redis to ensure no duplicates are stored")
