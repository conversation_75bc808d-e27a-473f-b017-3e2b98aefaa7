#!/usr/bin/env python3
"""
Test script to verify the API endpoints work and Redis duplicate fix
"""
import requests
import json
import time
import redis
import os
import ssl
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API configuration
API_BASE_URL = "http://localhost:8000"

# Redis configuration
REDIS_HOST = os.getenv("REDIS_HOST", "master.redis-cache.icgeht.use1.cache.amazonaws.com")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_SSL = os.getenv("REDIS_SSL", "true").lower() == "true"

def connect_redis():
    """Connect to Redis"""
    try:
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            db=REDIS_DB,
            ssl=REDIS_SSL,
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10,
            ssl_min_version=ssl.TLSVersion.TLSv1_2 if REDIS_SSL else None
        )
        redis_client.ping()
        return redis_client
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return None

def test_api_endpoint():
    """Test the API endpoint and check for duplicates in Redis"""
    print("🚀 Testing API endpoint and Redis duplicate fix...")
    
    # Connect to Redis
    redis_client = connect_redis()
    if not redis_client:
        print("❌ Cannot connect to Redis, skipping duplicate check")
        return False
    
    # Test data
    test_query = "What is artificial intelligence?"
    test_payload = {
        "query": test_query,
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "your-api-key-here",  # You'll need to provide a real API key
        "collections": ["Gen AI"]
    }
    
    try:
        # Create a new chat first
        print("📝 Creating new chat...")
        new_chat_response = requests.post(f"{API_BASE_URL}/new_chat")
        if new_chat_response.status_code != 200:
            print(f"❌ Failed to create new chat: {new_chat_response.status_code}")
            return False
        
        chat_data = new_chat_response.json()
        chat_id = chat_data.get("chat_id")
        print(f"✅ New chat created: {chat_id}")
        
        # Add chat_id to payload
        test_payload["chat_id"] = chat_id
        
        # Get user_id from the response or use a test one
        user_id = "test_user_123"  # This would normally come from the session
        
        # Clear any existing messages for this session
        session_key = f"session:{user_id}:{chat_id}"
        redis_client.delete(session_key)
        print(f"🧹 Cleared existing session: {session_key}")
        
        # Send the query
        print("📤 Sending query to API...")
        response = requests.post(f"{API_BASE_URL}/query", json=test_payload)
        
        if response.status_code == 200:
            print("✅ API call successful!")
            result = response.json()
            print(f"   Answer: {result.get('answer', 'No answer')[:100]}...")
            print(f"   Source: {result.get('source', 'No source')}")
            
            # Check Redis for duplicates
            print("\n🔍 Checking Redis for duplicates...")
            time.sleep(1)  # Give Redis a moment to update
            
            messages = redis_client.lrange(session_key, 0, -1)
            if messages:
                parsed_messages = [json.loads(msg) for msg in messages]
                user_queries = [msg for msg in parsed_messages if msg.get('role') == 'user']
                
                print(f"   Total messages in Redis: {len(parsed_messages)}")
                print(f"   User queries found: {len(user_queries)}")
                
                # Check for duplicates
                query_contents = [q.get('content') for q in user_queries]
                unique_queries = set(query_contents)
                
                if len(query_contents) == len(unique_queries):
                    print("✅ No duplicate queries found!")
                    return True
                else:
                    print("❌ Duplicate queries detected!")
                    for content in query_contents:
                        count = query_contents.count(content)
                        if count > 1:
                            print(f"   '{content}' appears {count} times")
                    return False
            else:
                print("⚠️ No messages found in Redis")
                return False
                
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Make sure the server is running on port 8000")
        return False
    except Exception as e:
        print(f"❌ Error during API test: {e}")
        return False

def test_server_health():
    """Test if the server is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"⚠️ Server responded with status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error checking server health: {e}")
        return False

def test_simple_query():
    """Test a simple query without requiring API keys"""
    print("📝 Testing simple query endpoint...")

    # Connect to Redis
    redis_client = connect_redis()
    if not redis_client:
        print("❌ Cannot connect to Redis, skipping duplicate check")
        return False

    try:
        # Create a new chat first
        print("📝 Creating new chat...")
        new_chat_response = requests.post(f"{API_BASE_URL}/new_chat")
        if new_chat_response.status_code != 200:
            print(f"❌ Failed to create new chat: {new_chat_response.status_code}")
            return False

        chat_data = new_chat_response.json()
        chat_id = chat_data.get("chat_id")
        print(f"✅ New chat created: {chat_id}")

        # Test a simple greeting that doesn't require API keys
        test_payload = {
            "query": "hello",
            "chat_id": chat_id,
            "mode": "agentic",
            "host": "groq",
            "model": "llama-3.3-70b-versatile",
            "api_key": "test-key"  # This should trigger a greeting response
        }

        user_id = "test_user_123"
        session_key = f"session:{user_id}:{chat_id}"

        # Clear any existing messages
        redis_client.delete(session_key)
        print(f"🧹 Cleared existing session: {session_key}")

        # Send the query
        print("📤 Sending greeting query...")
        response = requests.post(f"{API_BASE_URL}/query", json=test_payload)

        print(f"Response status: {response.status_code}")
        if response.status_code == 200:
            print("✅ API call successful!")

            # Check Redis for messages
            print("\n🔍 Checking Redis for messages...")
            time.sleep(2)  # Give Redis a moment to update

            messages = redis_client.lrange(session_key, 0, -1)
            if messages:
                parsed_messages = [json.loads(msg) for msg in messages]
                user_queries = [msg for msg in parsed_messages if msg.get('role') == 'user']

                print(f"   Total messages in Redis: {len(parsed_messages)}")
                print(f"   User queries found: {len(user_queries)}")

                # Show the messages
                for i, msg in enumerate(parsed_messages):
                    role = msg.get('role', 'unknown')
                    content = msg.get('content', '')[:50] + "..." if len(msg.get('content', '')) > 50 else msg.get('content', '')
                    print(f"   {i+1}. {role}: {content}")

                # Check for duplicates
                query_contents = [q.get('content') for q in user_queries]
                unique_queries = set(query_contents)

                if len(query_contents) == len(unique_queries):
                    print("✅ No duplicate queries found!")
                    return True
                else:
                    print("❌ Duplicate queries detected!")
                    for content in query_contents:
                        count = query_contents.count(content)
                        if count > 1:
                            print(f"   '{content}' appears {count} times")
                    return False
            else:
                print("⚠️ No messages found in Redis")
                return False

        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error during simple query test: {e}")
        return False

if __name__ == "__main__":
    print("🧪 API and Redis Duplicate Fix Test")
    print("=" * 50)
    
    # Test 1: Check if server is running
    print("1. Testing server health...")
    server_ok = test_server_health()
    
    if server_ok:
        # Test 2: Test simple query and Redis
        print("\n2. Testing simple query and Redis duplicate prevention...")
        api_ok = test_simple_query()

        print("\n" + "=" * 50)
        if api_ok:
            print("🎉 All tests passed! The Redis duplicate fix is working.")
        else:
            print("❌ Some tests failed. Check the output above.")
    else:
        print("\n" + "=" * 50)
        print("❌ Server is not running. Please start the server first:")
        print("   python main12.py")
        print("\nThen run this test again.")
    
    print("\n💡 Note: You may need to provide a valid API key in the test_payload")
    print("   to get a real response from the LLM service.")
