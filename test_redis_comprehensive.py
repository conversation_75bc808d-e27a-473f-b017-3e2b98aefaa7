#!/usr/bin/env python3
"""
Comprehensive test to verify Redis duplicate fix by directly testing the storage functions
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import redis
import json
import ssl
from datetime import datetime
from dotenv import load_dotenv
from bson import ObjectId

# Load environment variables
load_dotenv()

# Redis configuration
REDIS_HOST = os.getenv("REDIS_HOST", "master.redis-cache.icgeht.use1.cache.amazonaws.com")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_SSL = os.getenv("REDIS_SSL", "true").lower() == "true"

def connect_redis():
    """Connect to Redis"""
    try:
        redis_client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            db=REDIS_DB,
            ssl=REDIS_SSL,
            ssl_cert_reqs=None,
            ssl_check_hostname=False,
            decode_responses=True,
            socket_connect_timeout=10,
            socket_timeout=10,
            ssl_min_version=ssl.TLSVersion.TLSv1_2 if REDIS_SSL else None
        )
        redis_client.ping()
        return redis_client
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return None

def test_append_to_chat_history_function():
    """Test the append_to_chat_history function directly"""
    print("🧪 Testing append_to_chat_history function directly...")
    
    try:
        # Import the function from main12
        from main12 import append_to_chat_history, redis_client as main_redis_client
        
        if not main_redis_client:
            print("❌ Redis client not available in main12")
            return False
        
        # Test data
        test_user_id = "test_user_direct"
        test_chat_id = str(ObjectId())  # Generate a valid ObjectId
        test_query = "Test query for direct function test"
        test_response = "Test response for direct function test"
        
        session_key = f"session:{test_user_id}:{test_chat_id}"
        
        # Clear any existing data
        main_redis_client.delete(session_key)
        print(f"🧹 Cleared session: {session_key}")
        
        # Test 1: First call (should store messages)
        print("📝 First call to append_to_chat_history...")
        append_to_chat_history(
            chat_id=test_chat_id,
            query=test_query,
            response=test_response,
            user_id=test_user_id,
            sources=["test"],
            source_urls=[],
            mode="test",
            host="test",
            model="test",
            skip_redis=False
        )
        
        # Check Redis
        messages_after_first = main_redis_client.lrange(session_key, 0, -1)
        print(f"   Messages after first call: {len(messages_after_first)}")
        
        # Test 2: Second call with same query (should detect duplicate and skip Redis)
        print("📝 Second call to append_to_chat_history with same query...")
        append_to_chat_history(
            chat_id=test_chat_id,
            query=test_query,
            response=test_response,
            user_id=test_user_id,
            sources=["test"],
            source_urls=[],
            mode="test",
            host="test",
            model="test",
            skip_redis=False
        )
        
        # Check Redis again
        messages_after_second = main_redis_client.lrange(session_key, 0, -1)
        print(f"   Messages after second call: {len(messages_after_second)}")
        
        # Test 3: Third call with skip_redis=True (should skip Redis entirely)
        print("📝 Third call to append_to_chat_history with skip_redis=True...")
        append_to_chat_history(
            chat_id=test_chat_id,
            query="Different query",
            response="Different response",
            user_id=test_user_id,
            sources=["test"],
            source_urls=[],
            mode="test",
            host="test",
            model="test",
            skip_redis=True
        )
        
        # Check Redis final
        messages_after_third = main_redis_client.lrange(session_key, 0, -1)
        print(f"   Messages after third call: {len(messages_after_third)}")
        
        # Analyze results
        print("\n📊 Analysis:")
        if len(messages_after_first) == 2:
            print("✅ First call stored 2 messages (user + assistant)")
        else:
            print(f"❌ First call stored {len(messages_after_first)} messages (expected 2)")
        
        if len(messages_after_second) == len(messages_after_first):
            print("✅ Second call detected duplicate and didn't store additional messages")
        else:
            print(f"❌ Second call stored additional messages (now {len(messages_after_second)})")
        
        if len(messages_after_third) == len(messages_after_second):
            print("✅ Third call with skip_redis=True didn't store in Redis")
        else:
            print(f"❌ Third call with skip_redis=True still stored messages")
        
        # Show actual messages
        print("\n📋 Messages in Redis:")
        for i, msg in enumerate(messages_after_third):
            parsed_msg = json.loads(msg)
            role = parsed_msg.get('role', 'unknown')
            content = parsed_msg.get('content', '')[:50] + "..." if len(parsed_msg.get('content', '')) > 50 else parsed_msg.get('content', '')
            print(f"   {i+1}. {role}: {content}")
        
        # Clean up
        main_redis_client.delete(session_key)
        print("🧹 Test data cleaned up")
        
        # Return success if all tests passed
        return (len(messages_after_first) == 2 and 
                len(messages_after_second) == len(messages_after_first) and
                len(messages_after_third) == len(messages_after_second))
        
    except ImportError as e:
        print(f"❌ Could not import from main12: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing append_to_chat_history: {e}")
        return False

def test_memory_manager_function():
    """Test the memory manager store_interaction function"""
    print("\n🧪 Testing memory manager store_interaction function...")
    
    try:
        from main12 import memory_manager
        
        if not memory_manager:
            print("❌ Memory manager not available")
            return False
        
        # Test data
        test_user_id = "test_user_memory"
        test_chat_id = str(ObjectId())
        test_query = "Test query for memory manager"
        test_response = "Test response for memory manager"
        
        session_key = f"session:{test_user_id}:{test_chat_id}"
        
        # Clear any existing data
        if memory_manager.redis_client:
            memory_manager.redis_client.delete(session_key)
            print(f"🧹 Cleared session: {session_key}")
        
        # Store interaction
        print("📝 Storing interaction via memory manager...")
        memory_manager.store_interaction(
            query=test_query,
            response=test_response,
            chat_id=test_chat_id,
            user_id=test_user_id,
            metadata={"test": True}
        )
        
        # Check Redis
        if memory_manager.redis_client:
            messages = memory_manager.redis_client.lrange(session_key, 0, -1)
            print(f"   Messages stored: {len(messages)}")
            
            # Show messages
            for i, msg in enumerate(messages):
                parsed_msg = json.loads(msg)
                role = parsed_msg.get('role', 'unknown')
                content = parsed_msg.get('content', '')[:50] + "..." if len(parsed_msg.get('content', '')) > 50 else parsed_msg.get('content', '')
                print(f"   {i+1}. {role}: {content}")
            
            # Clean up
            memory_manager.redis_client.delete(session_key)
            print("🧹 Test data cleaned up")
            
            return len(messages) == 2
        else:
            print("⚠️ Redis client not available in memory manager")
            return False
        
    except ImportError as e:
        print(f"❌ Could not import memory manager: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing memory manager: {e}")
        return False

def check_existing_sessions():
    """Check for any existing sessions in Redis"""
    print("\n🔍 Checking for existing sessions in Redis...")
    
    redis_client = connect_redis()
    if not redis_client:
        return False
    
    try:
        # Get all session keys
        session_keys = redis_client.keys("session:*")
        print(f"Found {len(session_keys)} session keys")
        
        if session_keys:
            for key in session_keys[:5]:  # Show first 5
                messages = redis_client.lrange(key, 0, -1)
                print(f"  {key}: {len(messages)} messages")
                
                if messages:
                    # Check for duplicates in this session
                    parsed_messages = [json.loads(msg) for msg in messages]
                    user_queries = [msg for msg in parsed_messages if msg.get('role') == 'user']
                    
                    if len(user_queries) > 1:
                        query_contents = [q.get('content') for q in user_queries]
                        unique_queries = set(query_contents)
                        
                        if len(query_contents) != len(unique_queries):
                            print(f"    ⚠️ Duplicates found in {key}")
                            for content in query_contents:
                                count = query_contents.count(content)
                                if count > 1:
                                    print(f"      '{content}' appears {count} times")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking existing sessions: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Comprehensive Redis Duplicate Fix Test")
    print("=" * 60)
    
    # Test 1: Check existing sessions
    check_existing_sessions()
    
    # Test 2: Test append_to_chat_history function
    test1_ok = test_append_to_chat_history_function()
    
    # Test 3: Test memory manager function
    test2_ok = test_memory_manager_function()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"   append_to_chat_history test: {'✅ PASS' if test1_ok else '❌ FAIL'}")
    print(f"   memory_manager test: {'✅ PASS' if test2_ok else '❌ FAIL'}")
    
    if test1_ok and test2_ok:
        print("\n🎉 All tests passed! The Redis duplicate fix is working correctly.")
    else:
        print("\n❌ Some tests failed. The duplicate fix may need adjustment.")
    
    print("\n💡 The fix prevents duplicates by:")
    print("   1. Checking for existing queries before storing in append_to_chat_history")
    print("   2. Using skip_redis=True when memory_manager already stored the interaction")
    print("   3. Ensuring only one storage path is used per query/response pair")
